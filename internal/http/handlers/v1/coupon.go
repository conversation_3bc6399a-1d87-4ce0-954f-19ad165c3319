package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"slices"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/helpers"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/jackc/pgx/v4/pgxpool"
)

type CouponHandler struct {
	queries *postgres.Queries
	pool    *pgxpool.Pool
	logger  logger.Logger
}

// checkCouponPermission checks if a user has permission to access a coupon
func (h *CouponHandler) checkCouponPermission(ctx context.Context, user middlewares.User, coupon postgres.GetCouponByExternalIDRow) bool {
	// Admin users have access to all coupons
	if slices.Contains(user.Roles, "admin") {
		return true
	}

	// Check admin-owned coupons
	if coupon.OwnerType == "admin" && coupon.OwnerID == user.ID {
		return true
	}

	// Check company-owned coupons
	if coupon.OwnerType == "company" {
		company, err := h.queries.GetCompanyByID(ctx, coupon.OwnerID)
		if err == nil && company.OwnerID.Valid && company.OwnerID.Int32 == user.ID {
			return true
		}
	}

	return false
}

type CouponValidationMinOrderError struct {
	Message       string `json:"message"`
	MinOrderValue int32  `json:"min_order_value"`
}

func (e *CouponValidationMinOrderError) Error() string {
	data := map[string]interface{}{
		"message":         e.Message,
		"min_order_value": e.MinOrderValue,
	}
	b, _ := json.Marshal(data)
	return string(b)
}

type ValidateAndApplyCouponParams struct {
	Context             context.Context
	Queries             *postgres.Queries
	Pool                *pgxpool.Pool
	Logger              logger.Logger
	User                middlewares.User
	CouponCode          string
	CompanyID           int32
	Amount              int32
	ShouldRegisterUsage bool
}

type ApplyCouponRequest struct {
	CouponCode        string `json:"coupon_code" validate:"required"`
	OrderValue        int32  `json:"order_value" validate:"required"`
	CompanyExternalID string `json:"company_external_id" validate:"required"`
}

type ApplyCouponResponse struct {
	DiscountValue int32  `json:"discount_value"`
	FinalValue    int32  `json:"final_value"`
	Message       string `json:"message"`
}

type CreateCouponRequest struct {
	Code              string  `json:"code" validate:"required"`
	Type              string  `json:"type" validate:"required,oneof=percentage fixed"`
	Value             float64 `json:"value" validate:"required"`
	Quantity          int32   `json:"quantity" validate:"required"`
	ExpiresAt         string  `json:"expires_at" validate:"required"`
	MinOrderValue     float64 `json:"min_order_value" validate:"required"`
	OwnerType         string  `json:"owner_type" validate:"required,oneof=company admin"`
	CompanyExternalID string  `json:"company_external_id"`
}

type Coupon struct {
	Code            string    `json:"code"`
	Type            string    `json:"type"`
	Value           int32     `json:"value"`
	Quantity        int32     `json:"quantity"`
	UsedCount       int64     `json:"used_count"`
	TotalQuantity   int32     `json:"total_quantity"`
	IsActive        bool      `json:"is_active"`
	ExpiresAt       time.Time `json:"expires_at"`
	MinOrderValue   int32     `json:"min_order_value"`
	OwnerType       string    `json:"owner_type"`
	OwnerExternalID string    `json:"owner_external_id"`
	CompanyName     string    `json:"company_name"`
	CompanyCnpj     string    `json:"company_cnpj"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	ExternalID      string    `json:"external_id"`
}

type ListCouponsSuccessResponse = common.SuccessResponseWithPagination[[]Coupon]
type ListOneCouponSuccessResponse = common.SuccessResponse[Coupon]

// Helper functions for coupon value conversions

// CentsToReais converts cents to reais (e.g., 250 -> 2.50)
func CentsToReais(cents int32) float64 {
	return float64(cents) / 100
}

// ReaisToCents converts reais to cents (e.g., 2.50 -> 250)
func ReaisToCents(reais float64) int32 {
	return int32(math.Round(reais * 100))
}

// FormatCurrency formats cents as currency string (e.g., 250 -> "R$ 2,50")
func FormatCurrency(cents int32) string {
	return fmt.Sprintf("R$ %.2f", CentsToReais(cents))
}

// ConvertCouponValueToCents converts coupon value based on type
func ConvertCouponValueToCents(value float64, couponType string) int32 {
	if couponType == "fixed" {
		// For fixed values, convert reais to cents
		return ReaisToCents(value)
	} else if couponType == "percentage" {
		// For percentage, store as percentage * 100 (e.g., 10% = 1000)
		return int32(value * 100)
	}
	return 0
}

func NewCouponHandler(env *config.Environment, queries *postgres.Queries, pool *pgxpool.Pool, log logger.Logger) *chi.Mux {
	h := &CouponHandler{
		queries: queries,
		pool:    pool,
		logger:  log,
	}
	m := middlewares.New(env, queries)
	router := chi.NewRouter()
	defaultRouter := router.With(m.DefaultPermissions)
	adminRouter := router.With(m.AdminPermissions)

	defaultRouter.Post("/apply", h.ApplyCoupon)
	adminRouter.Post("/", h.CreateCoupon)
	adminRouter.Get("/", h.ListCoupons)
	adminRouter.Get("/{external_id}", h.GetCoupon)
	adminRouter.Patch("/{external_id}/status", h.UpdateStatus)

	return router
}

// ApplyCoupon godoc
// @Summary Aplica um cupom de desconto
// @Description Valida e aplica um cupom de desconto ao pedido
// @Tags Coupons
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body ApplyCouponRequest true "Dados para aplicar o cupom"
// @Success 200 {object} ApplyCouponResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/coupon/apply [post]
func (h *CouponHandler) ApplyCoupon(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	helper := helpers.New()
	var payload ApplyCouponRequest
	if r.Body != nil {
		defer r.Body.Close()
	}
	// Decodifica o payload
	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	company, err := h.queries.GetCompanyByExternalID(r.Context(), payload.CompanyExternalID)
	if err != nil {
		common.RespondError(w, err, 400)
		return
	}

	couponDiscount, _, err := ValidateAndApplyCoupon(ValidateAndApplyCouponParams{
		Context:             r.Context(),
		Queries:             h.queries,
		Pool:                h.pool,
		Logger:              h.logger,
		User:                user,
		CouponCode:          payload.CouponCode,
		CompanyID:           company.ID,
		Amount:              payload.OrderValue,
		ShouldRegisterUsage: false,
	})
	if err != nil {
		log.Println("Error applying coupon: ", err)
		// match with "valor mínimo"
		var minOrderErr *CouponValidationMinOrderError
		if errors.As(err, &minOrderErr) {
			err := &CouponValidationMinOrderError{
				Message:       minOrderErr.Message,
				MinOrderValue: couponDiscount, //TODO: workaround para retornar o valor mínimo em centavos
			}
			common.RespondError(w, err, 400)
			return
		}
		common.RespondError(w, err, 400)
		return
	}
	resp := ApplyCouponResponse{
		DiscountValue: couponDiscount,
		FinalValue:    payload.OrderValue - couponDiscount,
		Message:       "Cupom aplicado com sucesso!",
	}
	common.RespondSuccess(w, resp, http.StatusOK)
}

// ValidateAndApplyCoupon valida o cupom e retorna o desconto, owner_type e erro. Se shouldRegisterUsage for true, registra o uso e decrementa a quantidade.
func ValidateAndApplyCoupon(params ValidateAndApplyCouponParams) (int32, string, error) {
	// Use injected logger for dependency injection
	log := params.Logger

	// Log coupon validation start
	log.WithContext(params.Context).WithFields(map[string]interface{}{
		"coupon_code":    params.CouponCode,
		"amount":         params.Amount,
		"company_id":     params.CompanyID,
		"user_id":        params.User.ID,
		"register_usage": params.ShouldRegisterUsage,
	}).Info("Starting coupon validation")

	coupon, err := params.Queries.GetCouponByCode(params.Context, params.CouponCode)
	if err == sql.ErrNoRows {
		log.WithContext(params.Context).WithField("coupon_code", params.CouponCode).Warn("Coupon not found")
		return 0, "", errors.New("cupom não encontrado")
	} else if err != nil {
		if err.Error() == "no rows in result set" {
			log.WithContext(params.Context).WithFields(
				map[string]interface{}{
					"coupon_code":    params.CouponCode,
					"amount":         params.Amount,
					"company_id":     params.CompanyID,
					"user_id":        params.User.ID,
					"register_usage": params.ShouldRegisterUsage,
				},
			).Warn("Coupon not found")
			return 0, "", errors.New("cupom não encontrado")
		}
		log.WithContext(params.Context).WithError(err).WithField("coupon_code", params.CouponCode).Error("Database error while fetching coupon")
		return 0, "", err
	}

	// Log coupon details
	log.WithContext(params.Context).WithFields(map[string]interface{}{
		"coupon_id":         coupon.ID,
		"coupon_type":       coupon.Type,
		"coupon_value":      coupon.Value,
		"coupon_quantity":   coupon.Quantity,
		"coupon_active":     coupon.IsActive,
		"coupon_owner_type": coupon.OwnerType,
		"expires_at":        coupon.ExpiresAt,
	}).Debug("Coupon details retrieved")

	if coupon.OwnerType == "company" {
		if coupon.OwnerID != params.CompanyID {
			log.WithContext(params.Context).WithFields(map[string]interface{}{
				"coupon_owner_id": coupon.OwnerID,
				"company_id":      params.CompanyID,
			}).Warn("Coupon cannot be used at this company")
			return 0, "", errors.New("cupom não pode ser usado neste estabelecimento")
		}
	}

	if !coupon.IsActive {
		log.WithContext(params.Context).WithField("coupon_code", params.CouponCode).Warn("Coupon is inactive")
		return 0, "", errors.New("cupom inativo")
	}
	if coupon.ExpiresAt.Before(time.Now()) {
		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"coupon_code": params.CouponCode,
			"expires_at":  coupon.ExpiresAt,
		}).Warn("Coupon is expired")
		err := disableCoupon(params, coupon)
		if err != nil {
			log.WithContext(params.Context).WithError(err).WithField("coupon_code", params.CouponCode).Error("Failed to deactivate expired coupon")
		}
		return 0, "", errors.New("cupom expirado")
	}
	if coupon.Quantity <= 0 {
		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"coupon_code": params.CouponCode,
			"quantity":    coupon.Quantity,
		}).Warn("Coupon is out of stock")
		err := disableCoupon(params, coupon)
		if err != nil {
			log.WithContext(params.Context).WithError(err).WithField("coupon_code", params.CouponCode).Error("Failed to deactivate out of stock coupon")
		}
		return 0, "", errors.New("cupom esgotado")
	}

	used, err := params.Queries.HasUserUsedCoupon(params.Context, postgres.HasUserUsedCouponParams{
		UserID:   params.User.ID,
		CouponID: coupon.ID,
	})
	if err != nil {
		log.WithContext(params.Context).WithError(err).WithFields(map[string]interface{}{
			"user_id":   params.User.ID,
			"coupon_id": coupon.ID,
		}).Error("Database error while checking coupon usage")
		return 0, "", err
	}

	if used > 0 {
		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"user_id":     params.User.ID,
			"coupon_code": params.CouponCode,
		}).Warn("Coupon already used by this user")
		return 0, "", errors.New("cupom já utilizado por este usuário")
	}

	minOrderCents := coupon.MinOrderValue
	if minOrderCents == 0 {
		minOrderCents = 1500 // Default R$ 15.00
	}

	if params.Amount < minOrderCents {
		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"order_amount":    params.Amount,
			"min_order_value": minOrderCents,
			"coupon_code":     params.CouponCode,
		}).Warn("Order amount below minimum required for coupon")
		return minOrderCents, "", &CouponValidationMinOrderError{
			Message:       "Valor mínimo para uso do cupom: " + FormatCurrency(minOrderCents),
			MinOrderValue: minOrderCents,
		}
	}

	var couponDiscount int32
	valueCents := coupon.Value

	switch coupon.Type {
	case "fixed":
		couponDiscount = valueCents
		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"coupon_type":     "fixed",
			"coupon_value":    valueCents,
			"discount_amount": couponDiscount,
		}).Debug("Applied fixed discount coupon")
	case "percentage":
		// Value is stored as percentage * 100 (e.g., 10% = 1000)
		// So we divide by 10000 to get the actual percentage
		couponDiscount = int32(float64(params.Amount) * (float64(valueCents) / 10000))
		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"coupon_type":     "percentage",
			"coupon_value":    valueCents,
			"percentage":      float64(valueCents) / 100,
			"order_amount":    params.Amount,
			"discount_amount": couponDiscount,
		}).Debug("Applied percentage discount coupon")
	}

	if couponDiscount > params.Amount {
		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"original_discount": couponDiscount,
			"order_amount":      params.Amount,
			"final_discount":    params.Amount,
		}).Debug("Discount capped to order amount")
		couponDiscount = params.Amount
	}

	if params.ShouldRegisterUsage {
		log.WithContext(params.Context).WithField("coupon_code", params.CouponCode).Info("Registering coupon usage")

		// Start a transaction to ensure atomicity
		tx, err := params.Pool.Begin(params.Context)
		if err != nil {
			log.WithContext(params.Context).WithError(err).Error("Failed to begin transaction for coupon usage")
			return 0, "", err
		}
		defer tx.Rollback(params.Context)
		qtx := params.Queries.WithTx(tx)

		// Registra uso e decrementa quantidade (caso de checkout)
		err = qtx.RegisterCouponUsage(params.Context, postgres.RegisterCouponUsageParams{
			UserID:   params.User.ID,
			CouponID: coupon.ID,
		})
		if err != nil {
			log.WithContext(params.Context).WithError(err).WithFields(map[string]interface{}{
				"user_id":   params.User.ID,
				"coupon_id": coupon.ID,
			}).Error("Failed to register coupon usage")
			return 0, "", err
		}

		quantity, err := qtx.UseCoupon(params.Context, coupon.Code)
		if err != nil {
			log.WithContext(params.Context).WithError(err).WithField("coupon_code", coupon.Code).Error("Failed to use coupon")
			return 0, "", err
		}
		if quantity < 0 {
			log.WithContext(params.Context).WithFields(map[string]interface{}{
				"coupon_code":        coupon.Code,
				"remaining_quantity": quantity,
			}).Warn("Coupon out of stock after usage attempt")
			return 0, "", errors.New("cupom esgotado ou não disponível")
		}

		if err = tx.Commit(params.Context); err != nil {
			log.WithContext(params.Context).WithError(err).Error("Failed to commit coupon usage transaction")
			return 0, "", err
		}

		log.WithContext(params.Context).WithFields(map[string]interface{}{
			"coupon_code":        params.CouponCode,
			"remaining_quantity": quantity,
		}).Info("Coupon usage registered successfully")
	}

	// Log successful validation
	log.WithContext(params.Context).WithFields(map[string]interface{}{
		"coupon_code":      params.CouponCode,
		"discount_amount":  couponDiscount,
		"owner_type":       coupon.OwnerType,
		"usage_registered": params.ShouldRegisterUsage,
	}).Info("Coupon validation completed successfully")

	return couponDiscount, coupon.OwnerType, nil
}

func disableCoupon(params ValidateAndApplyCouponParams, coupon postgres.Coupon) error {
	err := params.Queries.UpdateCouponStatus(params.Context, postgres.UpdateCouponStatusParams{
		ExternalID: coupon.ExternalID,
		IsActive:   false,
	})
	return err
}

// CreateCoupon godoc
// @Summary Cria um novo cupom
// @Description Cria um novo cupom de desconto
// @Tags Coupons
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body CreateCouponRequest true "Dados do cupom"
// @Success 201 {string} string "Cupom criado com sucesso"
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/coupon [post]
func (h *CouponHandler) CreateCoupon(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	helper := helpers.New()
	var payload CreateCouponRequest
	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		log.Println("Validation errors: ", validationErrors)
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	// Convert values to cents using helper functions
	valueCents := ConvertCouponValueToCents(payload.Value, payload.Type)
	minOrderValueCents := ReaisToCents(payload.MinOrderValue)

	// ExpiresAT should be Brazilian time format
	expiresAt, err := time.Parse("02/01/2006 15:04:05", payload.ExpiresAt)
	if err != nil {
		common.RespondError(w, err, 400)
		return
	}

	var ownerID int32
	if payload.OwnerType == "company" {
		company, err := h.queries.GetCompanyByExternalID(r.Context(), payload.CompanyExternalID)
		if err != nil {
			log.Println("Failed to get company: ", err)
			common.RespondError(w, err, 400)
			return
		}
		ownerID = company.ID
	} else {
		ownerID = user.ID
	}

	externalID, err := h.queries.CreateCoupon(r.Context(), postgres.CreateCouponParams{
		Code:          payload.Code,
		Type:          payload.Type,
		Value:         valueCents,
		Quantity:      payload.Quantity,
		IsActive:      true,
		ExpiresAt:     expiresAt,
		MinOrderValue: minOrderValueCents,
		OwnerType:     payload.OwnerType,
		OwnerID:       ownerID,
		ExternalID:    helper.GenerateULIDV2(),
	})
	if err != nil {
		log.Println("Failed to create coupon: ", err)
		common.RespondError(w, err, 400)
		return
	}

	common.RespondSuccess(w, externalID, http.StatusCreated)
}

// ListCoupons godoc
// @Summary List all coupons
// @Description List all coupons with pagination
// @Tags Coupons
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} ListCouponsSuccessResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/coupon [get]
func (h *CouponHandler) ListCoupons(w http.ResponseWriter, r *http.Request) {
	helper := helpers.New()
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page, err := helper.ParseInt(pageStr, 1)
	if err != nil {
		common.RespondError(w, err, http.StatusBadRequest)
		return
	}

	limit, err := helper.ParseInt(limitStr, 10)
	if err != nil {
		common.RespondError(w, err, http.StatusBadRequest)
		return
	}

	offset := (page - 1) * limit

	coupons, err := h.queries.ListCoupons(r.Context(), postgres.ListCouponsParams{
		Limit:  int32(limit),
		Offset: int32(offset),
	})
	if err != nil {
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	response := make([]Coupon, len(coupons))
	totalItems := 0
	if len(coupons) > 0 {
		totalItems = int(coupons[0].TotalCount)
	}
	for i, c := range coupons {
		response[i] = Coupon{
			Code:            c.Code,
			Type:            c.Type,
			Value:           c.Value,
			Quantity:        c.Quantity,
			UsedCount:       c.UsedCount,
			TotalQuantity:   c.TotalQuantity,
			IsActive:        c.IsActive,
			ExpiresAt:       c.ExpiresAt,
			MinOrderValue:   c.MinOrderValue,
			OwnerType:       c.OwnerType,
			OwnerExternalID: c.OwnerExternalID,
			CompanyName:     c.OwnerName.String,
			CompanyCnpj:     c.OwnerCnpj.String,
			CreatedAt:       c.CreatedAt.Time,
			UpdatedAt:       c.UpdatedAt.Time,
			ExternalID:      c.ExternalID,
		}
	}

	common.RespondSuccessWithPagination(w, response, page, limit, totalItems)
}

// GetCoupon godoc
// @Summary Get a coupon by ExternalID
// @Description Get a specific coupon by its ExternalID
// @Tags Coupons
// @Accept json
// @Produce json
// @Security Bearer
// @Param external_id path string true "Coupon ExternalID"
// @Success 200 {object} ListOneCouponSuccessResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 404 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/coupon/{external_id} [get]
func (h *CouponHandler) GetCoupon(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "external_id")

	coupon, err := h.queries.GetCouponByExternalID(r.Context(), externalID)
	if err != nil {
		if err == sql.ErrNoRows {
			common.RespondError(w, errors.New("coupon not found"), http.StatusNotFound)
			return
		}
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	// Check if user has permission to view this coupon
	if !h.checkCouponPermission(r.Context(), user, coupon) {
		common.RespondError(w, errors.New("you are not allowed to view this coupon"), http.StatusForbidden)
		return
	}

	// Get usage count for this coupon
	usedCount, err := h.queries.GetCouponUsageCount(r.Context(), coupon.ID)
	if err != nil {
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	// Calculate total quantity (current remaining + used)
	totalQuantity := coupon.Quantity + int32(usedCount)

	response := Coupon{
		Code:            coupon.Code,
		Type:            coupon.Type,
		Value:           coupon.Value,
		Quantity:        coupon.Quantity,
		UsedCount:       usedCount,
		TotalQuantity:   totalQuantity,
		IsActive:        coupon.IsActive,
		ExpiresAt:       coupon.ExpiresAt,
		MinOrderValue:   coupon.MinOrderValue,
		OwnerType:       coupon.OwnerType,
		OwnerExternalID: coupon.OwnerExternalID,
		CompanyName:     coupon.OwnerName.String,
		CompanyCnpj:     coupon.OwnerCnpj.String,
		CreatedAt:       coupon.CreatedAt.Time,
		UpdatedAt:       coupon.UpdatedAt.Time,
		ExternalID:      coupon.ExternalID,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// UpdateStatus godoc
// @Summary Atualiza o status de um cupom
// @Description Ativa ou desativa um cupom através do parâmetro status
// @Tags Coupons
// @Accept json
// @Produce json
// @Security Bearer
// @Param external_id path string true "Coupon ExternalID"
// @Param status query bool true "true para ativar, false para desativar"
// @Success 200 {object} EmptySuccessResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 404 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/coupon/{external_id}/status [patch]
func (h *CouponHandler) UpdateStatus(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	externalID := chi.URLParam(r, "external_id")
	statusStr := r.URL.Query().Get("status")

	if statusStr == "" {
		common.RespondError(w, errors.New("status parameter is required"), http.StatusBadRequest)
		return
	}

	status, err := strconv.ParseBool(statusStr)
	if err != nil {
		common.RespondError(w, errors.New("invalid status parameter"), http.StatusBadRequest)
		return
	}

	coupon, err := h.queries.GetCouponByExternalID(r.Context(), externalID)
	if err != nil {
		if err.Error() == "no rows in result set" {
			h.logger.WithFields(map[string]interface{}{
				"external_id": externalID,
				"status":      status,
			}).Warn("Coupon not found")
			common.RespondError(w, errors.New("coupon not found"), http.StatusNotFound)
			return
		}
		h.logger.WithError(err).Error("Failed to get coupon by External ID")
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	// Check if user has permission to update this coupon
	if !h.checkCouponPermission(r.Context(), user, coupon) {
		common.RespondError(w, errors.New("not authorized"), http.StatusForbidden)
		return
	}

	err = h.queries.UpdateCouponStatus(r.Context(), postgres.UpdateCouponStatusParams{
		ExternalID: externalID,
		IsActive:   status,
	})
	if err != nil {
		h.logger.WithError(err).Error("Failed to update coupon status")
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

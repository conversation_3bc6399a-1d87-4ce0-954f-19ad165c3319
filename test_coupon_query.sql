-- Test the GetCouponByExternalID query
SELECT
    c.id, c.code, c.type, c.value, c.quantity, c.is_active, c.expires_at, c.min_order_value, c.owner_type, c.owner_id, c.external_id, c.created_at, c.updated_at,
    COALESCE(u.external_id, co.external_id) AS owner_external_id,
    co.name AS owner_name,
    co.cnpj AS owner_cnpj
FROM coupons c
LEFT JOIN users u
    ON c.owner_type = 'admin' AND c.owner_id = u.id
LEFT JOIN companies co
    ON c.owner_type = 'company' AND c.owner_id = co.id
WHERE c.external_id = '01K1CK7JP8WCR88DWYF3JFG7JT';
